import { filterComponentsMapping } from '@/pkgs/queries/filterComponentsMapping'
import { Alert, Checkbox, FormControlLabel, Grid, IconButton, MenuItem, Stack } from '@mui/material'
import Select from '@mui/material/Select'
import { Operation, isInvertible, Filter } from '@/pkgs/queries/types'
import DeleteIcon from '@mui/icons-material/Delete'
import { LightTooltip } from '@/common/components/LightTooltip'

type FilterComponentProps = {
    value: Filter
    label: string
    onChange: (value: Filter | null) => void
    options?: any[]
    config?: any
}

export function FilterComponent({ value, onChange, label, options, config }: FilterComponentProps) {
    const filterOptionsMap = filterComponentsMapping[value.FieldType]
    const filterOptions = Object.keys(filterOptionsMap)

    const FilterComponent = filterOptionsMap[value.Operation]
    if (!FilterComponent) {
        return (
            <Alert severity='error'>
                Filter not found: {value.Operation}/{value.Operation}
            </Alert>
        )
    }

    return (
        <Grid container spacing={1} sx={{ mb: 0.5 }}>
            <Grid item xs={2} alignContent={'center'}>
                {label}
            </Grid>
            <Grid item xs={2}>
                <Stack direction='row' spacing={1} alignItems='center'>
                    {isInvertible(value.Operation) && (
                        <LightTooltip title={'Invert filter'}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        value={!value.Inverted}
                                        onChange={() =>
                                            onChange({
                                                ...value,
                                                Inverted: !value.Inverted
                                            })
                                        }
                                    />
                                }
                                label={value.Inverted ? 'NOT' : ''}
                            />
                        </LightTooltip>
                    )}
                    <Select
                        size={'small'}
                        value={value.Operation}
                        onChange={(ev) =>
                            onChange({
                                ...value,
                                Operation: ev.target.value as Operation,
                                Inverted: false,
                                Value: undefined
                            })
                        }
                        renderValue={(value) => splitCamelCase(value as string)}
                    >
                        {filterOptions.map((option) => (
                            <MenuItem key={option} value={option}>
                                {splitCamelCase(option)}
                            </MenuItem>
                        ))}
                    </Select>
                </Stack>
            </Grid>
            <Grid item xs={true} alignContent={'center'}>
                <FilterComponent
                    value={value.Value}
                    onChange={(val) => onChange({ ...value, Value: val })}
                    options={options}
                    config={config}
                />
            </Grid>
            <Grid item xs={'auto'}>
                <IconButton size={'small'} color={'error'} onClick={() => onChange(null)}>
                    <DeleteIcon />
                </IconButton>
            </Grid>
        </Grid>
    )
}

function splitCamelCase(str: string): string {
    const spaced = str.replace(/([a-z])([A-Z])/g, '$1 $2')
    return spaced.toLowerCase()
}
