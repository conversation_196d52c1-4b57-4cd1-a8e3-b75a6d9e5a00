import _ from 'lodash'
import { Content } from '../../types'
import { Structure } from '@/pkgs/structure/types'
import _isEqual from '@/helpers/isEqual'
import { formatHTML } from '@/pkgs/lexical-editor/LexicalPlayground'
import moment from 'moment'

function rKeys(o: any) {
    if (!o || typeof o !== 'object') return []

    const paths: string[] = []
    const stack: { obj: any; path: string[] }[] = [{ obj: o, path: [] }]

    while (stack.length > 0) {
        const { obj, path } = stack.pop() as { obj: any; path: string[] }

        if (typeof obj === 'object' && obj !== null) {
            for (const key in obj) {
                stack.push({ obj: obj[key], path: [...path, key] })
            }
        } else {
            paths.push(path.join('.') + '')
        }
    }

    return paths
}

function differenceObject(object: any, base: any) {
    function changes(object: any, base: any) {
        return _.transform(object, (result: any, value, key) => {
            if (!_.isEqual(value, base?.[key])) {
                if (_.isObject(value) && _.isObject(base?.[key])) {
                    if (value instanceof Date && base?.[key] instanceof Date) {
                        if (value.getTime() !== base?.[key].getTime()) {
                            result[key] = value
                        }
                    } else {
                        result[key] = changes(value, base?.[key])
                    }
                } else {
                    result[key] = value
                }
            }
        })
    }

    return changes(object, base)
}

export function omitIrrelevantProps(value: any) {
    // Specify the keys to omit directly
    const directlyOmittedKeys = [
        'Created',
        'Deleted',
        'Updated',
        'Publisher',
        'Owner',
        'SysPeriod',
        'Approved',
        'CurrentEditor',
        'EditingSession',
        'ExtendedLock'
    ]

    const allPaths = rKeys(value)

    // From the user perspective we can ingore the Data.*.*.json and Data.*.*.engine keys (the relevant information is in the Data.*.*.html key)
    // Collect keys that match the Data.*.*.json pattern
    const isDataJsonKey = (key: string): boolean => /^Data\.[^.]+\.[^.]+\.json/.test(key)
    const dataJsonKeys = allPaths.filter((key) => isDataJsonKey(key)).map((key) => key.split('.json.')[0] + '.json')

    // Collect keys that match the Data.*.*.engine pattern
    const isEngineJsonKey = (key: string): boolean => /^Data\.[^.]+\.[^.]+\.engine/.test(key)
    const engineJsonKeys = allPaths.filter((key) => isEngineJsonKey(key))

    // Combine the directly omitted keys with the dynamically found keys
    const keysToOmit = [...directlyOmittedKeys, ...[...new Set(dataJsonKeys)], ...[...new Set(engineJsonKeys)]]
    // console.log('dataJsonKeys', dataJsonKeys)

    // Omit the specified keys from the value
    return _.omit(value, keysToOmit)
}

function getSectionChanges(originalContent, modifiedContent, sectionName) {
    let stateWithoutUpdated = omitIrrelevantProps(originalContent) as any
    let originalStateWithoutUpdated = omitIrrelevantProps(modifiedContent) as any
    const serverValueSectionChanges = stateWithoutUpdated?.Data?.[sectionName]
    const valueSectionChanges = originalStateWithoutUpdated?.Data?.[sectionName]

    const c1 = differenceObject(serverValueSectionChanges, valueSectionChanges)
    const c2 = differenceObject(valueSectionChanges, serverValueSectionChanges)

    return { Before: c1, After: c2 }
}

type Change = {
    originalValue?: string
    modifiedValue?: string
}

type ContentPartial = Partial<Record<keyof Content, Change | Record<string, Change>>>

export function getContentChanges(
    originalContent: Content | undefined,
    modifiedContent: Content | undefined,
    structure: Structure | undefined
) {
    const changes: ContentPartial = {}

    for (const key of Object.keys(omitIrrelevantProps(modifiedContent))) {
        if (key == 'Data') continue

        const isDate = moment(originalContent?.[key]).isValid() || moment(originalContent?.[key]).isValid()

        const hasChanges = isDate
            ? !moment(originalContent?.[key]).isSame(moment(modifiedContent?.[key]))
            : !_.isEqual(originalContent?.[key], modifiedContent?.[key])

        if (!hasChanges) continue

        changes[key] = {
            originalValue: originalContent?.[key],
            modifiedValue: modifiedContent?.[key]
        }
    }

    if (structure?.FormStructure) {
        for (const formStructure of structure?.FormStructure) {
            const sectionChanges = getSectionChanges(originalContent, modifiedContent, formStructure?.name)
            const hasChanges =
                !!Object.values(sectionChanges?.Before).length || !!Object.values(sectionChanges?.After).length

            if (!hasChanges) continue

            if (!('Data' in changes)) {
                changes['Data'] = {}
            }

            if (!(formStructure.name in changes['Data']!)) {
                changes['Data']![formStructure.name] = {}
            }

            if (formStructure?.allowMultiple || !formStructure?.components) {
                changes['Data']![formStructure.name] = {
                    originalValue: sectionChanges.Before,
                    modifiedValue: sectionChanges.After
                }
            } else {
                for (const component of formStructure.components) {
                    const isRichText = component.type == 'rich-text' // lexical
                    const originalValue: string = isRichText
                        ? formatHTML(originalContent?.Data?.[formStructure?.name]?.[component.name]?.['html'])
                        : originalContent?.Data?.[formStructure?.name]?.[component.name]

                    const modifiedValue: string = isRichText
                        ? formatHTML(modifiedContent?.Data?.[formStructure?.name]?.[component.name]?.['html'])
                        : modifiedContent?.Data?.[formStructure?.name]?.[component.name]

                    const hasChanges = !_isEqual(originalValue, modifiedValue)

                    if (!hasChanges) continue

                    changes['Data']![formStructure.name][component.name] = {
                        originalValue: originalValue,
                        modifiedValue: modifiedValue
                    }
                }
            }
        }
    }

    return changes
}
