import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { SxProps } from '@mui/material'
import { BlockOptionsDropdownList, getBlockTypeIcon, getBlockTypeLabel } from './BlockOptionsDropdownList'
import InsertOptionsDropdownList, { INSERT_OPTIONS } from './InsertOptionsDropdownList'
import { insertCollapsible } from '../Collapsible/CollapsiblePlugin'
import { insertMonacoCodeEditor } from '../MonacoCodeEmbed/MonacoCodeEmbedPlugin'
import InsertTableDialog from '../Table/InsertTableDialog'
import { ContentExplorerDialog } from '@/pkgs/content/explorer/ContentExplorerDialog'
import { ContentType } from '@/pkgs/content/types'
import { insertContentFragment } from '../ContentFragment/ContentFragmentPlugin'
import { InsertImageDialog } from '../Image/InsertImageDialog'
import { CodeNode } from '@lexical/code'
import { $isMonacoCodeEmbedNode, MonacoCodeEmbedNode } from '../MonacoCodeEmbed/MonacoCodeEmbedNode'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import CodeLanguagesDropdownList from './CodeLanguagesDropdownList'
import InsertLayoutDialog from '../Layout/InsertLayoutDialog'
import { ToolbarButton } from '@/pkgs/lexical-editor/toolbar/ToolbarButton'
import { toolbarButtons } from '@/pkgs/lexical-editor/toolbar/ToolbarButtons'
import { Container } from '@/pkgs/lexical-editor/toolbar/Container'
import { Divider } from '@/pkgs/lexical-editor/toolbar/Divider'
import { getDefaultConfig, LexicalToolbarConfig, TOOLBAR_BUTTON } from '@/pkgs/lexical-editor/toolbar/getDefaultConfig'
import {
    $getNodeByKey,
    $getSelection,
    $isNodeSelection,
    $isRangeSelection,
    $isRootOrShadowRoot,
    CAN_REDO_COMMAND,
    CAN_UNDO_COMMAND,
    CLICK_COMMAND,
    LexicalEditor,
    SELECTION_CHANGE_COMMAND
} from 'lexical'
import { $isCMHeadingNode } from '@/pkgs/lexical-editor/Anchors/CMHeadingNode'
import { getSelectedNode, LowPriority } from '@/pkgs/lexical-editor/toolbar/toolbar.helpers'
import { $isLinkNode } from '@/pkgs/lexical-editor/Link/LinkNode'
import { $getClosestAnchorNode } from '@/pkgs/lexical-editor/Anchors/utils'
import { BlockType, blockTypeToBlockName } from '@/pkgs/lexical-editor/toolbar/StickyToolbar'
import { $findMatchingParent, $getNearestNodeOfType, mergeRegister } from '@lexical/utils'
import { $isListNode, ListNode } from '@lexical/list'
import { $isCodeNode, CODE_LANGUAGE_MAP } from '@lexical/code'
import DocumentGallery from '@/pkgs/media/document/DocumentGallery'
import { insertDocumentLink } from '../DocumentLink/DocumentLinkPlugin'
import { LexicalEditorOptions } from '../RichTextEditor'
import { insertQuery } from '../QueryNode/QueryPlugin'
import QueryDialog from '../QueryNode/QueryDialog'

export interface LexicalToolbarButtonConfig {
    disabled?: boolean // visible but not actionable
    hide?: boolean // not visible
    active?: boolean // visible, actionable and highlighted
}

export interface LexicalToolbarProps {
    editorOptions?: LexicalEditorOptions
    variant: 'floating' | 'fixed'
    sx?: SxProps
    disabled: boolean
}

export const LexicalToolbar = forwardRef(({ sx, editorOptions, variant, disabled }: LexicalToolbarProps, ref) => {
    const [editor] = useLexicalComposerContext()
    const formatButtonRef = useRef(null)
    const insertButtonRef = useRef(null)

    const [insertDropdownIsVisible, setInsertDropdownIsVisible] = useState(false)
    const [insertTableDialogIsOpen, setInsertTableDialogIsOpen] = useState(false)
    const [blockOptionsDropdownIsVisible, setBlockOptionsDropdownIsVisible] = useState(false)
    const [isMediaGalleryOpen, setIsMediaGalleryOpen] = useState(false)
    const [documentGalleryIsOpen, setDocumentGalleryIsOpen] = useState(false)
    const [contentExplorerDialogIsOpen, setContentExplorerDialogIsOpen] = useState(false)
    const [insertLayoutDialogIsOpen, setInsertLayoutDialogIsOpen] = useState(false)
    const [queryDialogIsOpen, setQueryDialogIsOpen] = useState(false)

    const { toolbarConfig, codeLanguage, onCodeLanguageSelect, blockType } = useLexicalToolbar(
        editor,
        editorOptions?.toolbar,
        disabled
    )

    const isHTMLEmbedBlock = blockType === MonacoCodeEmbedNode.getType()
    const isCodeBlock = blockType === CodeNode.getType()

    useEffect(() => {
        setInsertDropdownIsVisible(false)
        setInsertTableDialogIsOpen(false)
        setBlockOptionsDropdownIsVisible(false)
        setContentExplorerDialogIsOpen(false)
        setInsertLayoutDialogIsOpen(false)
        setQueryDialogIsOpen(false)
    }, [blockType])

    if (isHTMLEmbedBlock) {
        return (
            <Container ref={ref} sx={sx} className={variant}>
                {toolbarButtons.undo(editor, toolbarConfig?.undo)}
                {toolbarButtons.redo(editor, toolbarConfig?.redo)}
            </Container>
        )
    }

    if (isCodeBlock) {
        return (
            <Container ref={ref} sx={sx} className={variant}>
                {toolbarButtons.undo(editor, toolbarConfig?.undo)}
                {toolbarButtons.redo(editor, toolbarConfig?.redo)}
                {!toolbarConfig?.undo?.hide || (!toolbarConfig?.redo?.hide && <Divider />)}
                {toolbarConfig?.format?.hide ? null : (
                    <>
                        <ToolbarButton
                            ref={formatButtonRef}
                            disabled={toolbarConfig?.format?.disabled}
                            onClick={() => {
                                setBlockOptionsDropdownIsVisible(!blockOptionsDropdownIsVisible)
                                setInsertDropdownIsVisible(false)
                            }}
                            aria-label='Formatting Options'
                            isDropdown
                            icon={getBlockTypeIcon(blockType)}
                            label={getBlockTypeLabel(blockType)}
                        />

                        {blockOptionsDropdownIsVisible && (
                            <BlockOptionsDropdownList
                                editor={editor}
                                blockType={blockType}
                                buttonRef={formatButtonRef}
                                onClose={() => setBlockOptionsDropdownIsVisible(false)}
                            />
                        )}
                        <Divider />
                    </>
                )}
                {toolbarConfig?.code?.hide || !codeLanguage || !onCodeLanguageSelect ? null : (
                    <CodeLanguagesDropdownList value={codeLanguage} onChange={onCodeLanguageSelect} />
                )}
            </Container>
        )
    }

    return (
        <Container ref={ref} sx={sx} className={variant}>
            {variant !== 'floating' && toolbarButtons.undo(editor, toolbarConfig?.undo)}
            {variant !== 'floating' && toolbarButtons.redo(editor, toolbarConfig?.redo)}
            {variant !== 'floating' && (!toolbarConfig?.undo?.hide || !toolbarConfig?.redo?.hide) && <Divider />}

            {toolbarConfig?.format?.hide ? null : (
                <>
                    <ToolbarButton
                        ref={formatButtonRef}
                        disabled={toolbarConfig?.format?.disabled}
                        onClick={() => {
                            setBlockOptionsDropdownIsVisible(!blockOptionsDropdownIsVisible)
                            setInsertDropdownIsVisible(false)
                        }}
                        aria-label='Formatting Options'
                        isDropdown
                        icon={getBlockTypeIcon(blockType)}
                        label={getBlockTypeLabel(blockType)}
                    />

                    {blockOptionsDropdownIsVisible && (
                        <BlockOptionsDropdownList
                            editor={editor}
                            blockType={blockType}
                            buttonRef={formatButtonRef}
                            onClose={() => setBlockOptionsDropdownIsVisible(false)}
                        />
                    )}
                    <Divider />
                </>
            )}
            {variant === 'floating' || toolbarConfig?.insert?.hide ? null : (
                <>
                    <ToolbarButton
                        ref={insertButtonRef}
                        disabled={toolbarConfig?.insert?.disabled}
                        onClick={() => {
                            setInsertDropdownIsVisible(!insertDropdownIsVisible)
                            setBlockOptionsDropdownIsVisible(false)
                        }}
                        aria-label='Insert Node Options'
                        icon={'add'}
                        label='Insert'
                        isDropdown
                    />
                    <Divider />
                    {insertDropdownIsVisible && (
                        <InsertOptionsDropdownList
                            editor={editor}
                            blockType={blockType}
                            buttonRef={insertButtonRef}
                            insertOptionsConfig={Object.values(INSERT_OPTIONS).filter(
                                (insertOption) => !toolbarConfig?.insert?.[insertOption]?.hide
                            )}
                            closeDropdown={() => setInsertDropdownIsVisible(false)}
                            tableButtonOnClick={() => setInsertTableDialogIsOpen(true)}
                            imageButtonOnClick={() => setIsMediaGalleryOpen(true)}
                            documentLinkButtonOnClick={() => setDocumentGalleryIsOpen(true)}
                            contentFragmentButtonOnClick={() => setContentExplorerDialogIsOpen(true)}
                            collapsibleButtonOnClick={() => {
                                insertCollapsible(editor)
                            }}
                            codeEmbedButtonOnClick={() => insertMonacoCodeEditor(editor, '')}
                            layoutButtonOnClick={() => setInsertLayoutDialogIsOpen(true)}
                            queryButtonOnClick={() => setQueryDialogIsOpen(true)}
                        />
                    )}
                    {insertTableDialogIsOpen && (
                        <InsertTableDialog
                            activeEditor={editor}
                            isOpen={insertTableDialogIsOpen}
                            onClose={() => setInsertTableDialogIsOpen(false)}
                        />
                    )}

                    <ContentExplorerDialog
                        isOpen={contentExplorerDialogIsOpen}
                        onClose={() => setContentExplorerDialogIsOpen(false)}
                        contentTypes={[ContentType.Fragment]}
                        sites={[]}
                        onSelect={(id, content) => {
                            insertContentFragment(editor, content)
                            setContentExplorerDialogIsOpen(false)
                        }}
                        structureID={null}
                    />

                    <InsertImageDialog
                        activeEditor={editor}
                        isOpen={isMediaGalleryOpen}
                        onClose={() => setIsMediaGalleryOpen(false)}
                        allowedCropSizes={editorOptions?.allowedCropSizes}
                    />

                    <DocumentGallery
                        isGalleryOpen={documentGalleryIsOpen}
                        setGalleryClose={() => setDocumentGalleryIsOpen(false)}
                        isForDct={true}
                        saveForDct={(doc) => {
                            insertDocumentLink(editor, doc.id)
                            setDocumentGalleryIsOpen(false)
                        }}
                    />

                    {insertLayoutDialogIsOpen && (
                        <InsertLayoutDialog activeEditor={editor} onClose={() => setInsertLayoutDialogIsOpen(false)} />
                    )}

                    {queryDialogIsOpen && (
                        <QueryDialog editor={editor} onClose={() => setQueryDialogIsOpen(false)} open={queryDialogIsOpen} />
                    )}
                </>
            )}

            {toolbarButtons.bold(editor, toolbarConfig?.bold)}
            {toolbarButtons.italic(editor, toolbarConfig?.italic)}
            {toolbarButtons.underline(editor, toolbarConfig?.underline)}
            {toolbarButtons.strikethrough(editor, toolbarConfig?.strikethrough)}

            {toolbarButtons.link(editor, toolbarConfig?.link)}
            {toolbarButtons.anchor(editor, toolbarConfig?.anchor)}

            {variant === 'floating' ? null : (
                <>
                    {(!toolbarConfig?.left?.hide ||
                        !toolbarConfig?.right?.hide ||
                        !toolbarConfig?.center?.hide ||
                        !toolbarConfig?.justify?.hide) && <Divider />}

                    {toolbarButtons.left(editor, toolbarConfig?.left)}
                    {toolbarButtons.right(editor, toolbarConfig?.right)}
                    {toolbarButtons.center(editor, toolbarConfig?.center)}
                    {toolbarButtons.justify(editor, toolbarConfig?.justify)}
                </>
            )}
        </Container>
    )
})

// Custom hook: useLexicalToolbar
// TODO: refactor ASAP
function useLexicalToolbar(
    editor: LexicalEditor,
    config: (TOOLBAR_BUTTON | INSERT_OPTIONS)[] | undefined,
    disabled: boolean
) {
    const [toolbarConfig, setToolbarConfig] = useState<LexicalToolbarConfig>(
        getDefaultConfig(Object.values(TOOLBAR_BUTTON), null, config || [], disabled)
    )

    const [selectedElementKey, setSelectedElementKey] = useState<string | null>(null)
    const [codeLanguage, setCodeLanguage] = useState<string>('')
    const [blockType, setBlockType] = useState<BlockType>('paragraph')

    const supportedBlockTypes = useMemo(() => new Set(Object.keys(blockTypeToBlockName)), [])

    // Update toolbar configuration when disabled prop changes
    useEffect(() => {
        setToolbarConfig(getDefaultConfig(Object.values(TOOLBAR_BUTTON), null, config || [], disabled))
    }, [config, disabled])

    const onCodeLanguageSelect = useCallback(
        (value: string) => {
            editor.update(() => {
                if (selectedElementKey !== null) {
                    const node = $getNodeByKey(selectedElementKey)
                    if ($isCodeNode(node)) {
                        node.setLanguage(value)
                    }
                }
            })
        },
        [editor, selectedElementKey]
    )

    const updateToolbarButtonConfig = (toolbarButton: TOOLBAR_BUTTON, value: LexicalToolbarButtonConfig) => {
        setToolbarConfig((prev) => ({
            ...prev,
            [toolbarButton]: { ...toolbarConfig?.[toolbarButton], ...value }
        }))
    }

    const updateToolbar = useCallback(() => {
        editor.getEditorState().read(() => {
            const selection = $getSelection()
            if (
                ($isNodeSelection(selection) && $isMonacoCodeEmbedNode(selection?.getNodes()?.[0])) ||
                ($isRangeSelection(selection) && $isMonacoCodeEmbedNode(selection.focus.getNode()))
            ) {
                setBlockType(MonacoCodeEmbedNode.getType())
            } else if (!supportedBlockTypes.has(selection?.getNodes()?.[0]?.getType() || '')) {
                // Reset blockType / toolbar to default
                setBlockType('paragraph')
            }

            if (!$isRangeSelection(selection)) {
                const newToolbarConfig = {
                    anchor: {
                        ...toolbarConfig?.anchor,
                        active: Boolean($getClosestAnchorNode(selection)?.getId()),
                        disabled: disabled || !$getClosestAnchorNode(selection)
                    }
                }
                setToolbarConfig((prev) => ({ ...prev, ...newToolbarConfig }))
                return
            }

            const anchorNode = selection.anchor.getNode()
            let element =
                anchorNode.getKey() === 'root'
                    ? anchorNode
                    : $findMatchingParent(anchorNode, (e) => {
                          const parent = e.getParent()
                          return parent !== null && $isRootOrShadowRoot(parent)
                      })

            if (element === null) {
                element = anchorNode.getTopLevelElementOrThrow()
            }

            const elementKey = element.getKey()
            const elementDOM = editor.getElementByKey(elementKey)
            if (elementDOM !== null) {
                setSelectedElementKey(elementKey)
                if ($isListNode(element)) {
                    const parentList = $getNearestNodeOfType(anchorNode, ListNode)
                    const type = parentList ? parentList.getTag() : element.getTag()
                    setBlockType(type)
                } else {
                    const type = $isCMHeadingNode(element) ? element.getTag() : element.getType()
                    if (type in blockTypeToBlockName) {
                        setBlockType(type as keyof typeof blockTypeToBlockName)
                    }

                    if ($isCodeNode(element)) {
                        const language = element.getLanguage() as keyof typeof CODE_LANGUAGE_MAP
                        setCodeLanguage(language ? CODE_LANGUAGE_MAP[language] || language : '')
                        return
                    }
                }
            }
            const node = getSelectedNode(selection)
            const parent = node.getParent()

            const newToolbarConfig = {
                bold: {
                    ...toolbarConfig?.bold,
                    active: selection.hasFormat('bold')
                },
                italic: {
                    ...toolbarConfig?.italic,
                    active: selection.hasFormat('italic')
                },
                underline: {
                    ...toolbarConfig?.underline,
                    active: selection.hasFormat('underline')
                },
                strikethrough: {
                    ...toolbarConfig?.strikethrough,
                    active: selection.hasFormat('strikethrough')
                },
                code: {
                    ...toolbarConfig?.code,
                    active: selection.hasFormat('code')
                },
                link: {
                    ...toolbarConfig?.link,
                    active: $isLinkNode(parent) || $isLinkNode(node)
                },
                anchor: {
                    ...toolbarConfig?.anchor,
                    active: Boolean($getClosestAnchorNode(selection)?.getId()),
                    disabled: disabled || !$getClosestAnchorNode(selection)
                }
            }
            setToolbarConfig((prev) => ({ ...prev, ...newToolbarConfig }))
        })
    }, [editor, supportedBlockTypes, toolbarConfig, disabled])

    useEffect(() => {
        return mergeRegister(
            editor.registerUpdateListener(({ editorState }) => {
                editorState.read(() => {
                    updateToolbar()
                })
            }),
            editor.registerCommand(
                SELECTION_CHANGE_COMMAND,
                () => {
                    updateToolbar()
                    return false
                },
                LowPriority
            ),
            editor.registerCommand(
                CLICK_COMMAND,
                () => {
                    updateToolbar()
                    return false
                },
                LowPriority
            ),
            editor.registerCommand(
                CAN_UNDO_COMMAND,
                (payload) => {
                    updateToolbarButtonConfig(TOOLBAR_BUTTON.UNDO, { disabled: disabled || !payload })
                    return false
                },
                LowPriority
            ),
            editor.registerCommand(
                CAN_REDO_COMMAND,
                (payload) => {
                    updateToolbarButtonConfig(TOOLBAR_BUTTON.REDO, { disabled: disabled || !payload })
                    return false
                },
                LowPriority
            )
        )
    }, [editor, updateToolbar, updateToolbarButtonConfig, disabled])

    return {
        toolbarConfig,
        codeLanguage,
        onCodeLanguageSelect,
        blockType
    }
}
