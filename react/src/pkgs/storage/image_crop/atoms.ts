import { ImageCropSize } from '@/pkgs/system/image-crop-size/types'
import { atom } from 'jotai'

export const selectedSizeAtom = atom<ImageCropSize | null>(null)

// // original image id: crop sizes to be replaced
// const cropsToBeReplacedAtom = atom<Record<string, string[]>>({})

// export function useCropsToBeReplacedAtom(image: Image) {
//     const [cropsToBeReplaced, setCropsToBeReplaced] = useAtom(cropsToBeReplacedAtom)

//     function addCrops() {
//         if (!image) return

//         setCropsToBeReplaced({
//             ...cropsToBeReplaced,
//             [image.id]: image?.image_crop_size_ids || []
//         })
//     }

//     function removeCrop(targetCropSizeId: string) {
//         if (!image) return

//         setCropsToBeReplaced({
//             ...cropsToBeReplaced,
//             [image.id]: cropsToBeReplaced?.[image.id].filter((cropId) => cropId != targetCropSizeId)
//         })
//     }

//     return { cropsToBeReplaced: cropsToBeReplaced?.[image?.id], addCrops, removeCrop }
// }
