import { contentEditor<PERSON>tom } from '@/pkgs/auth/atoms'
import { ContentType } from '@/pkgs/content/types'
import { useAtomValue } from 'jotai'

interface Path {
    url: string
    pageId: string
    subPaths: string[] | null
    isCurrentPage: (path: string) => boolean
    isRecord: boolean
}

function isIdString(str: string) {
    return str.replace(/[^-]/g, '').length == 4
}

const contentTypeToPath = {
    [ContentType.Page]: '/pages',
    [ContentType.News]: '/news',
    [ContentType.Event]: '/events'
}

export default function usePath(): Path {
    // example href: http://localhost:3000/system/templates?siteId=5d2b935d-5435-483c-a8fe-d5f11ec7aa52
    const url = window?.location?.href || '/'

    const split = url.split('/')

    const hasSubPaths = split.length > 4
    const subPaths = hasSubPaths ? split.slice(3, split.length - 1) : null

    let currentPageId = split[split.length - 1]

    if (currentPageId.includes('?siteId')) {
        currentPageId = currentPageId.split('?')[0]
    }

    function isCurrentPage(path: string) {
        if (path.includes('/')) {
            const subPaths = path.split('/')
            return subPaths[subPaths.length - 1] == currentPageId
        }
        return path == currentPageId
    }

    return {
        url,
        pageId: currentPageId,
        subPaths,
        isCurrentPage,
        isRecord: isIdString(currentPageId)
    }
}
