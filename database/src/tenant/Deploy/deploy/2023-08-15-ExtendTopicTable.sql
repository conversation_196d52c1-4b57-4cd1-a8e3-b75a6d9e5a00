BEGIN;

ALTER TABLE topic ADD COLUMN IF NOT EXISTS title character varying(165);
ALTER TABLE topic ADD COLUMN IF NOT EXISTS description character varying(165);
ALTER TABLE topic ALTER COLUMN topic_type TYPE VARCHAR(30);
CREATE INDEX IF NOT EXISTS ix_topic_search ON topic USING gin ((id || topic_type || ' ' || coalesce(topic.title, ' ') || ' ' || coalesce(topic.description, ' ') || ' ') gin_trgm_ops);
CREATE INDEX IF NOT EXISTS ix_topic_type on topic USING btree(topic_type);

COMMIT;
