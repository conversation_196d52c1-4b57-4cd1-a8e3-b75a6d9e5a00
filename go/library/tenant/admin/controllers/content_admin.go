package adminControllers

import (
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/tenant/admin/services"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/pkgs/auth/permissions"
	"contentmanager/pkgs/auth/permissions/evaluators"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/content/admin"
	"contentmanager/pkgs/reservation"
	"encoding/json"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/http"
	"time"
)

type (
	ContentController struct{}
)

/* TODO -> MT: V1 Content
Confirm uses of V1 content API from the front end.
- Resources in its entirety
- Certain grids (Options through context menu, e.g Expire or Restore)
-

*/

// ----------------   API   ---------------- //

/*
GetAdminContent MT: Used
- PageManager
- Alerts Grid (GetAll, GetAllByClassification),
- Resources.jsx > loadContentFromServer & searchForContentBySearchTerm
- NavigationNodes.tsx > Search
*/
func (cc ContentController) GetAdminContent(w http.ResponseWriter, r *shared.AppContext) {
	params := admin.ParamsFromMap(r.Request().Form).
		WithPrivacyLevel(r.PublicAccount().PrivacyLevel).
		With(admin.WithTags).
		WithAdminData().
		WithSite(r.CurrentSite())

	contents, t, e := admin.GetByParams(r.TenantDatabase(), params)
	if e != nil {
		http.Error(w, e.Error(), http.StatusBadRequest)
		return
	}

	sites, err := r.Sites()
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	siteMap := tenancyModels.Sites(sites).ToMap()
	for i, ref := range contents {
		if ref.DepartmentId.Valid {
			contents[i].Department = siteMap[ref.DepartmentId.UUID].BaseSite
		}
	}

	utils.WriteResponseJSON(w, utils.ResponseObject{
		Results: contents,
		ResultSet: utils.ResponseResultSet{
			TotalRecords: int(t),
			Offset:       params.Skip,
			Limit:        params.Limit,
		},
	}, nil)
}

/*
GetAdminContentById MT: Used
- Alerts.jsx > setMenuElement
- CombinedNewsEvents.jsx > GetAllContent
- PageManager.jsx > GetAllContent
- Resources.jsx > setMenuElement
- ResourceEditor
*/
func (cc ContentController) GetAdminContentById(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id := uuid.FromStringOrNil(p["id"])
	if id == uuid.Nil {
		http.Error(w, "invalid id provided", http.StatusBadRequest)
		return
	}
	content, err := adminServices.GetContentById(r.TenantDatabase(), r.CurrentSiteID(), id, r.PublicAccount().PrivacyLevel)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		http.Error(w, err.Error(), http.StatusExpectationFailed)
		return
	}
	utils.EncodeJSON(w, r.Request(), content)
}

/*
PostContent
Deprecated - still in use in navigation and too brittle to update without substantial refactor.
*/
func (cc ContentController) PostContent(w http.ResponseWriter, r *shared.AppContext) {
	patchedMap := map[string]interface{}{}
	if err := binding.JSON.Bind(r.Request(), &patchedMap); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	inbound, err := contentFromMap(commonModels.Content{}, patchedMap)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if inbound.ID == uuid.Nil {
		inbound.ID = uuid.NewV4()
	}
	inbound.Active = true

	if err := evaluators.ForCreateObject(r, &inbound); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	if inbound.Type != commonModels.ExternalLinkContentType {
		http.Error(w, "Non external link used with `/api/v1/content`. Upgrade to `/api/v1/content`", http.StatusBadRequest)
		return
	}

	account := r.Account()
	inbound.Owner = account.ID
	inbound.Publisher = account.ID
	inbound.Created = time.Now()
	inbound.Updated = time.Now()

	viewModel, err := adminServices.CreateAndFetchContent(r, inbound)
	if err != nil {
		utils.ResponseJson(w, utils.Message("not able to create requested content: \n "+err.Error()), http.StatusBadRequest)
		return
	}
	utils.EncodeJSON(w, r.Request(), viewModel)
}

/*
	PatchContentById MT: Used

- ContentPinnedToggleMenuItem
- CombinedNewsEvents.jsx > handleSetExpiration
- DepartmentWidgetForEditor > handleChange
- Navigation.jsx > updateActiveExternalNode
- NavigationLanding.jsx > saveNavigationTree
*/
func (cc ContentController) PatchContentById(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	patchedID, idErr := uuid.FromString(p["id"])
	if idErr != nil {
		http.Error(w, idErr.Error(), http.StatusBadRequest)
		return
	}
	var source commonModels.Content
	if err := evaluators.ForActionByID(r, &source, patchedID, "update"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	// Not ideal, but don't want to refactor the fn.
	var dbContent content.Content
	if err := r.TenantDatabase().Where("id = ?", patchedID).First(&dbContent).Error; err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	// StartOrExtendSession has empty Params here to avoid starting a session
	// it may be more intuitive to have `dbContent.IsEditingSessionForUserOrAvailable(nil, &r.Account().ID)` since we're not interested in the `res.apply` here.
	if err := dbContent.StartOrExtendSession(reservation.ReservableParams{}, r.Account()); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	patchedMap := map[string]interface{}{}
	if err := binding.JSON.Bind(r.Request(), &patchedMap); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	source, err := contentFromMap(source, patchedMap)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	source.Publisher = r.Account().ID
	source.Updated = time.Now()

	if err := permissions.ValidateRoute(r.TenantDatabase(), source); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if err := permissions.ValidateSitesForSharable(r, source); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if err := permissions.Evaluate(r.Account(), source, "update"); err != nil {
		http.Error(w, "You are can't perform this update. You don't have permissions for this change. ", http.StatusForbidden)
		return
	}

	if err := r.TenantDatabase().Save(&source).Error; err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	utils.EncodeJSON(w, r.Request(), source)
}

func contentFromMap(source commonModels.Content, patchedMap map[string]interface{}) (commonModels.Content, error) {

	// id
	if inf, ok := patchedMap["id"]; ok {
		if patchedID, success := inf.(string); success {
			if parsedID, err := uuid.FromString(patchedID); err == nil {
				source.ID = parsedID
			} else {
				return source, err
			}
		} else {
			return source, errors.New("id must be a string")
		}
	}

	// settings
	// settings are replaced entirely
	if inf, ok := patchedMap["settings"]; ok {
		if b, e := json.Marshal(inf); e == nil {
			source.Settings = b
		} else {
			return source, e
		}
	}

	// structure
	if inf, ok := patchedMap["structure"]; ok {
		if b, e := json.Marshal(inf); e == nil {
			source.Structure = b
		} else {
			return source, e
		}
	}

	// data
	if inf, ok := patchedMap["data"]; ok {
		if b, e := json.Marshal(inf); e == nil {
			source.Data = b
		} else {
			return source, e
		}
	}

	// meta
	if inf, ok := patchedMap["meta"]; ok {
		if b, e := json.Marshal(inf); e == nil {
			source.Meta = b
		} else {
			return source, e
		}
	}

	// departmentId
	if inf, ok := patchedMap["departmentId"]; ok {
		source.DepartmentId = utils.InterfaceToNullUUID(inf)
	}

	// mediaId
	if inf, ok := patchedMap["mediaId"]; ok {
		source.MediaID = utils.InterfaceToNullUUID(inf)
	}

	// structureId
	if inf, ok := patchedMap["structureId"]; ok {
		if inf == nil {
			source.StructureID = nil
		} else if patchedStructureID, success := inf.(string); success {
			if parsedStructureID, err := uuid.FromString(patchedStructureID); err == nil {
				source.StructureID = &parsedStructureID
			} else {
				return source, err
			}
		} else {
			return source, errors.New("structureId must be a string")
		}
	}

	//sites
	if inf, ok := patchedMap["sites"]; ok {
		if b, e := json.Marshal(inf); e == nil {
			var patchedSites []uuid.UUID
			if e := json.Unmarshal(b, &patchedSites); e == nil {
				source.Sites = patchedSites
			} else {
				return source, e
			}
		} else {
			return source, e
		}
	}

	//route
	if inf, ok := patchedMap["route"]; ok {
		if patchedRoute, success := inf.(string); success {
			source.Route = patchedRoute
		} else {
			return source, errors.New("route must be a string")
		}
	}

	// pagelayout
	if inf, ok := patchedMap["pagelayout"]; ok {
		if patchedPageLayout, success := inf.(string); success {
			source.PageLayout = commonModels.NewPageType(patchedPageLayout)
		} else {
			return source, errors.New("pageLayout must be a valid PageType")
		}
	}

	// path
	if inf, ok := patchedMap["path"]; ok {
		if patchedPath, success := inf.(string); success {
			source.Path = patchedPath
		} else {
			return source, errors.New("path must be a string")
		}
	}

	// privacyLevel
	if inf, ok := patchedMap["privacyLevel"]; ok {
		if patchedPrivacyLevel, success := inf.(float64); success {
			source.PrivacyLevel = int(patchedPrivacyLevel)
		} else {
			return source, errors.New("privacyLevel must be a valid PrivacyLevel")
		}
	}

	//title
	if inf, ok := patchedMap["title"]; ok {
		if patchedTitle, success := inf.(string); success {
			source.Title = patchedTitle
		} else {
			return source, errors.New("title must be a string")
		}
	}

	//type
	if inf, ok := patchedMap["type"]; ok {
		if patchedType, success := inf.(string); success {
			parsedType := commonModels.NewContentType(patchedType)
			if len(parsedType) == 0 {
				return source, errors.New(fmt.Sprintf("type must be a commonModels.ContentType but it is: %s", patchedType))
			}
			source.Type = parsedType
		} else {
			return source, errors.New("type must be a commonModels.ContentType")
		}
	}

	//content
	if inf, ok := patchedMap["content"]; ok {
		if patchedContent, success := inf.(string); success {
			source.Content = patchedContent
		} else {
			return source, errors.New("content must be a string")
		}
	}

	// publish_at
	if inf, ok := patchedMap["publish_at"]; ok {
		if inf == nil {
			source.PublishAt = nil
		} else if patchedPublishAt, success := inf.(string); success {
			if parsedPublishAt, err := time.Parse(time.RFC3339, patchedPublishAt); err == nil {
				source.PublishAt = &parsedPublishAt
			} else {
				return source, err
			}
		} else {
			return source, errors.New("publishAt must be a string")
		}
	}

	// expire_at
	if inf, ok := patchedMap["expire_at"]; ok {
		if inf == nil {
			source.ExpireAt = nil
		} else if patchedExpireAt, success := inf.(string); success {
			if parsedExpireAt, err := time.Parse(time.RFC3339, patchedExpireAt); err == nil {
				source.ExpireAt = &parsedExpireAt
			} else {
				return source, err
			}
		} else {
			return source, errors.New("expireAt must be a string")
		}
	}

	// active
	if inf, ok := patchedMap["active"]; ok {
		if patchedActive, success := inf.(bool); success {
			source.Active = patchedActive
		} else {
			return source, errors.New("active must be a bool")
		}
	}

	// approved
	if inf, ok := patchedMap["approved"]; ok {
		if patchedApproved, success := inf.(bool); success {
			source.Approved = patchedApproved
		} else {
			return source, errors.New("approved must be a bool")
		}
	}

	// tags
	if inf, ok := patchedMap["tags"]; ok {
		if b, e := json.Marshal(inf); e == nil {
			var patchedTags []commonModels.Tag
			if e := json.Unmarshal(b, &patchedTags); e == nil {
				source.Tags = patchedTags
			} else {
				return source, e
			}
		} else {
			return source, e
		}
	}

	//structures
	if inf, ok := patchedMap["structures"]; ok {
		if b, e := json.Marshal(inf); e == nil {
			var patchedStructures []uuid.UUID
			if e := json.Unmarshal(b, &patchedStructures); e == nil {
				source.Structures = patchedStructures
			} else {
				return source, e
			}
		} else {
			return source, e
		}
	}

	return source, nil
}
