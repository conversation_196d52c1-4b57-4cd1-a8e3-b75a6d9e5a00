package adminDataaccess

import (
	"contentmanager/library/tenant/common/tests"
	"contentmanager/pkgs/content/admin"
	"contentmanager/tests"
	uuid "github.com/satori/go.uuid"
	"testing"
)

// this test fails, dont update -- changes are made in a separate branch
type testCase struct {
	Context  string
	Params   admin.Params
	Expected int
}

func Test_GetContentByParams(t *testing.T) {
	t.Skip("Skipping test. To work properly generated pages need to contain a valid `path`.")

	ctx := tests.InitLogging("GetAndCompileSearchResults")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	tenancyDb, mtDispose := tests.InitMultitenancyDB()
	defer mtDispose()

	var context = commonTests.Start(tenancyDb, db, commonTests.TestOptions{
		WithRegularSiteContent:    true,
		WithDepartmentSiteContent: true,
	})

	var tests = []testCase{
		{
			Context: "Default request from a Department Site",
			Params: admin.Params{
				Limit:            9999,
				ContentType:      []admin.Type{admin.Page},
				SiteId:           context.Department.ID,
				PrivacyLevel:     2,
				Departments:      []uuid.UUID{},
				IncludeAdminData: true,
				IsDepartment:     true,
			},
			Expected: 5,
		},
		// Not implemented
		//{
		//	Context: "Search for Department Content by site",
		//	Params: admincontent.Params{
		//		ContentType:          []admincontent.Type{admincontent.Page},
		//		SiteId:               uuid.Nil,
		//		PrivacyLevel:         2,
		//		Departments:          nil,
		//		IsDepartment:         false,
		//	},
		//},
		{
			Context: "Default request from a regular site",
			Params: admin.Params{
				Limit:            9999,
				ContentType:      []admin.Type{admin.Page},
				SiteId:           context.Site.ID,
				PrivacyLevel:     2,
				Departments:      []uuid.UUID{},
				IncludeAdminData: true,
				IsDepartment:     false,
			},
			Expected: 15,
		},
		{
			Context: "Search Request from a regular site for departmentId",
			Params: admin.Params{
				Limit:            9999,
				ContentType:      []admin.Type{admin.Page},
				SiteId:           context.Site.ID,
				PrivacyLevel:     2,
				Departments:      []uuid.UUID{context.Department.ID},
				IncludeAdminData: true,
				IsDepartment:     false,
			},
			Expected: 5,
		},
	}

	for _, ref := range tests {
		t.Log("Starting: ", ref.Context)
		results, total, err := admin.GetByParams(db.WithContext(ctx), ref.Params)
		if err != nil {
			t.Error(err)
			continue
		}
		if int(total) != ref.Expected {
			t.Errorf("Expected [%v], received [%v]", ref.Expected, total)
		}
		if len(results) != ref.Expected {
			t.Errorf("Expected [%v], received [%v]", ref.Expected, len(results))
		}
	}
}
