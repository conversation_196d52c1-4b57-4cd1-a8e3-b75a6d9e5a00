package publicServices

import (
	"contentmanager/library/shared"
	"contentmanager/library/tenant/public/utils/handlebars"
	_ "embed"
	"net/http"
)

//go:embed templates/accessdenied.tmpl
var AccessDeniedSource string
var AccessDeniedTemplate = handlebars.MustParse(AccessDeniedSource)

//go:embed templates/error.tmpl
var ErrorTemplateSource string
var ErrorTemplate = handlebars.MustParse(ErrorTemplateSource)

//go:embed templates/folder.tmpl
var FolderTemplateSource string
var FolderTemplate = handlebars.MustParse(FolderTemplateSource)

//go:embed templates/notfound.tmpl
var NotFoundTemplateSource string
var NotFoundTemplate = handlebars.MustParse(NotFoundTemplateSource)

//go:embed templates/search.tmpl
var SearchTemplateSource string
var SearchTemplate = handlebars.MustParse(SearchTemplateSource)

//go:embed templates/whoops.tmpl
var WhoopsTemplateSource string
var WhoopsTemplate = handlebars.MustParse(WhoopsTemplateSource)

//go:embed templates/azurelogin.tmpl
var AzureLoginTemplateSource string
var AzureLoginTemplate = handlebars.MustParse(AzureLoginTemplateSource)

func WriteNotFound(w http.ResponseWriter, r *shared.AppContext) {
	writeTemplate(w, r, NotFoundTemplate, "Not Found")
}
func WriteWhoops(w http.ResponseWriter, r *shared.AppContext) {
	writeTemplate(w, r, WhoopsTemplate, "Whoops")
}
func WriteError(w http.ResponseWriter, r *shared.AppContext) {
	writeTemplate(w, r, ErrorTemplate, "Error")
}

func writeTemplate(w http.ResponseWriter, r *shared.AppContext, template *handlebars.Template, title string) {
	if r == nil || template == nil {
		http.NotFound(w, r.Request())
		return
	}
	var nf = struct{ CurrentTitle string }{
		CurrentTitle: title,
	}
	compiled, err := CompileTemplate(r, nf, template)
	if err != nil {
		http.NotFound(w, r.Request())
		return
	}
	w.Write([]byte(compiled))
}
