package result

import (
	"contentmanager/library/utils/converters"
	"net/http"
)

type IResult interface {
	IsError() bool
	IsSuccess() bool
	Unwrap() error
	GetData() interface{}
	GetStatusCode() int
}

type Result[T any] struct {
	EmptyResult
	Data T
}

var _ IResult = (*Result[any])(nil)

func (r Result[T]) GetData() interface{} {
	return r.Data
}

func Error[T any](err error, zero T) Result[T] {
	return Result[T]{
		EmptyResult: EmptyResult{
			StatusCode:   http.StatusBadRequest,
			Success:      false,
			ErrorMessage: converters.AsPointer(err.Error()),
			error:        err,
		},
	}
}

func ErrorT[T any](err error) Result[T] {
	var zero T
	return Error(err, zero)
}

func Success[T any](result T) Result[T] {
	return Result[T]{
		EmptyResult: EmptyResult{
			StatusCode: http.StatusOK,
			Success:    true,
		},
		Data: result,
	}
}

func Check[T any](result T, err error) Result[T] {
	if err == nil {
		return Success(result)
	}
	return Error(err, result)
}

func CheckEmpty(err error) EmptyResult {
	if err == nil {
		return SuccessEmpty()
	}
	return ErrorEmpty(err)
}
