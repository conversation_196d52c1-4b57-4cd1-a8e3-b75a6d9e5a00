package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"encoding/json"
	"errors"
	"reflect"
	"strings"
)

func init() {
	handlebars.RegisterHelper("existsJsonbFieldPath", ifValidJson)
	handlebars.RegisterHelper("jsonbFieldPathHasLength", jsonbFieldPathHasLength)

	handlebars.RegisterHelper("jsonExists", ifValidJson)
	handlebars.RegisterHelper("jsonHasLength", jsonbFieldPathHasLength)
}

func ifValidJson(value json.RawMessage, path string, options *handlebars.Options) interface{} {
	valueAtPath, err := GetJsonAtPath(value, path)
	if err != nil && !errors.Is(err, InvalidJsonAtPath) {
		options.DataFrame().GetLogger().Err(err).Str("path", path).Msg("error getting valid JSON")
	}
	if valueAtPath == nil {
		return options.Inverse()
	} else {
		return options.Fn()
	}
}

func jsonbFieldPathHasLength(value json.RawMessage, path string, options *handlebars.Options) interface{} {
	var ok bool
	var result map[string]interface{}

	if value == nil {
		return options.Inverse()
	}

	json.Unmarshal(value, &result)
	if result == nil {
		return options.Inverse()
	}

	pathArray := strings.Split(path, ".")

	if len(pathArray) == 1 {
		v := reflect.ValueOf(result[pathArray[0]])
		r := v.Kind()
		if r != reflect.Invalid && v.Type().String() == "[]interface {}" && reflect.ValueOf(result[pathArray[0]]).Len() > 0 {
			return options.Fn()
		}
		return options.Inverse()
	}

	for _, item := range pathArray[:len(pathArray)-1] {
		result, ok = result[item].(map[string]interface{})
		if !ok {
			return ""
		}
	}

	item := pathArray[len(pathArray)-1]
	v := reflect.ValueOf(result[item])
	r := v.Kind()
	if r == reflect.Invalid && reflect.ValueOf(result[item]).Len() > 0 {
		return options.Inverse()
	}

	return options.Fn()
}
