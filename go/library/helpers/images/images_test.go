package images

import (
	"contentmanager/library/helpers"
	hbsHelpers "contentmanager/library/templates/hbs_helpers"
	"contentmanager/library/templates/hbs_helpers/keys"
	shared2 "contentmanager/library/templates/hbs_helpers/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	publicModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/viewmodels"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"testing"
	"time"
)

func Test_img(t *testing.T) {
	// when comparing the actual output to the expected output, the order of the characters and whitespaces are ignored
	// so *.reference can be formatted differently for convenience
	helpers.RunHandlebarFilesTests(t, ctx, mockHelpers, helpers.IgnoreCharsOrderComparer)
}

func mockHelpers() shared2.IHbsHelpers {
	return hbsHelpers.NewForTestsOnlyHbsHelpersFromRepository(helpers.NewMockRequest("https://example.com/path?page=1&random=111"), keys.NewKeys(), nil)
}

func ctx() interface{} {
	return &viewmodels.BaseContentPage{
		Account: nil,
		Site: tenancyModels.SiteViewModel{
			Site: tenancyModels.Site{
				BaseSite: tenancyModels.BaseSite{
					ID:          uuid.Nil,
					TenantID:    uuid.Nil,
					Name:        "Sample site name",
					Description: "",
					Type:        "",
					Active:      true,
				},
				Created:     time.Now(),
				Settings:    nil,
				Tags:        nil,
				Hosts:       nil,
				Departments: nil,
			},
			SiteSettings: tenancyModels.SiteSettings{},
		},
		Dct: json.RawMessage{},
		DctMap: map[string]interface{}{
			"sectionName": map[string]interface{}{
				"value": "Sample title",
			},
		},
		CurrentTitle:        "",
		TransportationAlert: false,
		Seo:                 viewmodels.Seo{},
		ContentTags:         nil,
		EventTags:           nil,
		DistributedContent:  "",
		PrimaryNavigation:   nil,
		Content: publicModels.ContentForHandlebars{
			Media: commonModels.Media{
				ID: uuid.FromStringOrNil("********-e479-4192-95bf-9acc0017c975"),
			},
		},
		News:                    nil,
		Events:                  nil,
		SharedNews:              nil,
		SiteNews:                nil,
		NavigationContent:       publicModels.ContentForHandlebars{},
		CurrentContent:          publicModels.ContentForHandlebars{},
		NavigationItems:         nil,
		NavigationParent:        publicModels.ContentForHandlebars{},
		MasterClassifiedContent: nil,
		TaggedPages:             nil,
	}
}
