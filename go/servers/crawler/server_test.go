package main

import (
	"net/url"
	"testing"
)

func Test_ProdVsLocal(t *testing.T) {
	//t.Skip()
	//urls := []string{
	//	"https://aecross-cbe.cmdesign.imagineeverything.com.localhost/",
	//}
	//for _, u := range urls {
	//	local := client.GetURLData(mustParseURL(u))
	//	prod := client.GetURLData(mustParseURL(strings.Replace(u, ".localhost", "", 1)))
	//
	//	dmp := diffmatchpatch.New()
	//	diffs := dmp.DiffMain(prod.Content, strings.ReplaceAll(local.Content, ".localhost", ""), false)
	//
	//	var filteredDiffs []diffmatchpatch.Diff
	//	for _, diff := range diffs {
	//
	//		if diff.Type == diffmatchpatch.DiffEqual {
	//			truncated := diffmatchpatch.Diff{Type: diffmatchpatch.DiffEqual, Text: truncateMiddle(diff.Text, 50)}
	//			filteredDiffs = append(filteredDiffs, truncated)
	//		} else {
	//			filteredDiffs = append(filteredDiffs, diff)
	//		}
	//
	//	}
	//	if len(filteredDiffs) == 0 {
	//		t.Log("No differences found.")
	//		continue
	//	}
	//	t.Log(dmp.DiffPrettyText(filteredDiffs))
	//}
}

func mustParseURL(str string) *url.URL {
	parsed, err := url.Parse(str)
	if err != nil {
		panic(err)
	}
	return parsed
}

func lastChars(s string, n int) string {
	runes := []rune(s)
	if len(runes) > n {
		return string(runes[len(runes)-n:])
	}
	return s
}
func truncateMiddle(s string, n int) string {
	runes := []rune(s)
	if len(runes) <= n {
		return s
	}

	placeholder := "<...>"
	placeholderRunes := []rune(placeholder)
	maxLen := n - len(placeholderRunes)

	if maxLen <= 0 {
		return placeholder // n is too small to include any original chars
	}

	partLen := maxLen / 2

	// Use rune slices to ensure proper handling of multibyte characters
	start := string(runes[:partLen])
	end := string(runes[len(runes)-(maxLen-partLen):])

	return start + placeholder + end
}
