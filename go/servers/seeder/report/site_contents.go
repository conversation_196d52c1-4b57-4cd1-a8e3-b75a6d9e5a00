package report

import (
	tenancyModels "contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/servers/seeder/db"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strconv"
	"time"
)

type siteContent struct {
	Title        string
	Type         string
	Site         string
	EditLink     string
	PublicLink   string
	PrivacyLevel string
	PublishAt    string
	ExpireAt     string
}

func GetSiteContent(dbHost, tenantId string) [][]string {
	var siteMap = getSites(dbHost, tenantId)
	var contents, documents = getContentAndDocs(dbHost)
	var rows = [][]string{
		siteContent{}.CSVHeaders(),
	}
	for _, document := range documents {
		var row = siteContent{
			Title:     document.Filename,
			Type:      "document",
			EditLink:  "N/A",
			PublishAt: "N/A",
			ExpireAt:  "N/A",
		}
		site := siteMap[document.Sites[0]]
		if len(document.Sites) == 1 {
			row.Site = site.Name
		} else {
			row.Site = fmt.Sprintf("%s Sites", strconv.Itoa(len(document.Sites)))
		}
		if document.PrivacyLevel == 0 {
			row.PrivacyLevel = "Public"
		} else {
			row.PrivacyLevel = "Staff"
		}
		row.PublicLink = fmt.Sprintf("https://%s/documents/%s", site.PrimaryDomain, document.ID.String())
		rows = append(rows, row.CSVRow())
	}

	for _, content := range contents {
		site := siteMap[content.Sites[0]]
		var row = siteContent{
			Title:        content.Title,
			EditLink:     fmt.Sprintf("https://contentmanager.imagineeverything.com/content-editor/%s?siteId=%s", content.ID.String(), site.ID.String()),
			PublicLink:   "https://" + site.PrimaryDomain + content.Route,
			Type:         content.Type.String(),
			PrivacyLevel: strconv.Itoa(content.PrivacyLevel),
		}
		if len(content.Sites) == 1 {
			row.Site = site.Name
		} else {
			row.Site = fmt.Sprintf("%s Sites", strconv.Itoa(len(content.Sites)))
		}
		if content.PublishAt == nil {
			row.PublishAt = "draft"
		} else {
			if content.PublishAt.After(time.Now()) {
				row.PublishAt = content.PublishAt.Format("2006-01-02 15:04:05")
			} else {
				row.PublishAt = "published"
			}
		}
		if content.ExpireAt != nil {
			row.ExpireAt = content.ExpireAt.Format("2006-01-02 15:04:05")
		}
		if content.PrivacyLevel == 0 {
			row.PrivacyLevel = "Public"
		} else {
			row.PrivacyLevel = "Staff"
		}
		rows = append(rows, row.CSVRow())
	}
	return rows
}

func getContentAndDocs(dbHost string) ([]commonModels.Content, []commonModels.Document) {
	db, sqlI := db.MustGetConnection(dbHost, "cm_peel")
	defer sqlI.Close()

	var documents []commonModels.Document
	if db.
		Select("id, filename, sites, privacy_level ").
		Where("type = ?", commonModels.File).
		Where("active").
		Where("cardinality(sites) > 0").
		Find(&documents).Error != nil {
		panic(db.Error)
	}

	var content []commonModels.Content
	if db.
		Select("id, title, route, sites, type, privacy_level, publish_at, expire_at ").
		Where("type in ('page', 'news', 'event') ").
		Where("active").
		Where("cardinality(sites) > 0").
		Find(&content).Error != nil {
		panic(db.Error)
	}
	return content, documents
}

func getSites(dbHost, tenantId string) map[uuid.UUID]tenancyModels.Site {
	db, sqlI := db.MustGetConnection(dbHost, "cm_multitenancy")
	defer sqlI.Close()

	var sites []tenancyModels.Site
	db.
		Where("active").
		Where("tenant_id = ?", tenantId).
		Find(&sites)
	if db.Error != nil {
		panic(db.Error)
	}
	return slicexx.AsMap(sites, func(site tenancyModels.Site) uuid.UUID {
		return site.ID
	})
}
