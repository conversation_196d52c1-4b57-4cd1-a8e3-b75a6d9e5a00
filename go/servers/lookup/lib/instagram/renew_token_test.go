package instagram

/* Deprecated (2025-01-07) No longer offer instagram support.

import (
	"contentmanager/library/utils/converters"
	"encoding/json"
	"testing"
	"time"
)

func Test_Renew(t *testing.T) {
	t.Skip("For manual testing only. Provide a valid token to test")
	// Arrange
	prev := time.Now().UTC().Add(-time.Hour * 1000)
	token := ExtendedToken{
		Token:      "<valid token>",
		ExpiresAt:  prev,
		LastError:  nil,
		LastUsedAt: &prev,
	}
	// Act
	res := RenewTokenIfNecessary(token)
	// Assert
	if res.LastError != nil {
		t.Errorf("RenewTokenIfNecessary() error = %v", res.LastError)
		return
	}
	if res.ExpiresAt.IsZero() {
		t.<PERSON><PERSON>("RenewTokenIfNecessary() res.ExpiresAt is zero")
		return
	}
	b, _ := json.Marshal(res.ExpiresAt)
	t.Logf("RenewTokenIfNecessary() res = %s", string(b))

	// Act 2
	res2 := RenewTokenIfNecessary(res)
	// Assert 2
	if res2.LastError != nil {
		t.Erro<PERSON>("RenewTokenIfNecessary() error = %v", res2.LastError)
		return
	}
	if res2.ExpiresAt != res.ExpiresAt {
		t.Errorf("RenewTokenIfNecessary() res.ExpiresAt is not equal")
		return
	}
}

func Test_NoActionIfNotTimeoutError(t *testing.T) {
	prev := time.Now().UTC().Add(-time.Hour * 1000)
	token := ExtendedToken{
		Token:      "",
		ExpiresAt:  prev,
		LastError:  converters.AsPointer("some error"),
		LastUsedAt: &prev,
	}

	res := RenewTokenIfNecessary(token)
	if res != token {
		t.Errorf("RenewTokenIfNecessary() res is not equal")
		return
	}
}

func Test_NoActionIfFreshToken(t *testing.T) {
	now := time.Now().UTC()
	token := ExtendedToken{
		Token:      "valid",
		ExpiresAt:  now.Add(time.Hour * 1000),
		LastError:  nil,
		LastUsedAt: &now,
	}

	res := RenewTokenIfNecessary(token)
	if res != token {
		t.Errorf("RenewTokenIfNecessary() res is not equal")
		return
	}
}

func Test_InvalidToken(t *testing.T) {
	t.Skip("For manual testing only. ")
	now := time.Now().UTC()
	token := ExtendedToken{
		Token:      "invalid",
		ExpiresAt:  now,
		LastError:  nil,
		LastUsedAt: &now,
	}

	res := RenewTokenIfNecessary(token)
	if res.LastError == nil {
		t.Errorf("RenewTokenIfNecessary() res.LastError is nil")
		return
	}
}
*/
