package polygonxx

import (
	"contentmanager/tests"
	"contentmanager/tests/test_utils"
	_ "encoding/xml"
	"testing"
)

func Test_PointInPolygon(t *testing.T) {
	db, dispose := tests.InitTenantDB()
	defer dispose()

	// Convert kml xml data to a MapOverlayPolygon from its lat lng coordinate vertices
	// kml data is an overlay from a CBE school (Sir Wilfrid Laurier School)
	testKmlData := test_utils.ReadFileFromTestData("cbe_wilfridlaurier_overlay.kml")

	polygon, err := KmlToPolygon(testKmlData)
	if err != nil {
		t.Error(err)
	}
	overlay := MapOverlayPolygon{Polygon: polygon}
	if err = db.Create(&overlay).Error; err != nil {
		t.Error(err)
	}

	var polygonMatches []MapOverlayPolygon
	var pointInAlberta = GeometricPoint{X: -113.987523, Y: 51.042727}
	if err = db.Where("polygon @> ?", pointInAlberta.String()).Find(&polygonMatches).Error; err != nil {
		t.<PERSON>r(err)
	}
	if len(polygonMatches) != 1 {
		t.Errorf("expected 1 result, got %d", len(polygonMatches))
	}
	var pointInVancouver = GeometricPoint{X: -121.951330, Y: 49.109850}
	if err = db.Where("polygon @> ?", pointInVancouver.String()).Find(&polygonMatches).Error; err != nil {
		t.Error(err)
	}
	if len(polygonMatches) != 0 {
		t.Errorf("expected 0 result, got %d", len(polygonMatches))
	}
}

// https://gis.stackexchange.com/questions/429633/how-to-add-kml-file-to-postgresql-as-polygon-in-nodejs
