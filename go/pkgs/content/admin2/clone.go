package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/library/utils"
	"contentmanager/pkgs/content"
	"github.com/goccy/go-json"
	"github.com/satori/go.uuid"
	"strings"
)

// CloneContent
// CreateContent manages fields ID | Path | Owner | Publisher and timestamps
func CloneContent(r *shared.AppContext, contentToClone content.Content) result.Result[uuid.UUID] {
	var DTO ContentDTO
	// Path needs to have its last segment removed, otherwise the cloned page will have
	// its template ancestor be the page it was cloned from.
	oldPathSuffix := utils.SanitizeLTree(contentToClone.ID.String())
	DTO.Path = strings.Replace(contentToClone.Path, "."+oldPathSuffix, "", 1)
	DTO.Sites = contentToClone.Sites
	DTO.DepartmentID = contentToClone.DepartmentID
	DTO.Type = contentToClone.Type
	DTO.Title = cloneTitle(contentToClone)
	DTO.StructureID = contentToClone.StructureID
	DTO.Data = contentToClone.Data
	DTO.Structure = contentToClone.Structure
	DTO.Content = contentToClone.Content
	DTO.Route = cloneRoute(contentToClone, uuid.NewV4().String())
	DTO.PageLayout = contentToClone.PageLayout
	DTO.MediaID = contentToClone.MediaID
	DTO.PublishAt = nil
	DTO.ExpireAt = nil
	DTO.Meta = contentToClone.Meta
	DTO.Settings = cloneSettingsWithoutPrimacy(contentToClone.Settings)
	DTO.Tags = contentToClone.Tags

	return CreateContent(r, DTO)
}
func cloneSettingsWithoutPrimacy(settings json.RawMessage) json.RawMessage {
	var m = map[string]interface{}{}
	json.Unmarshal(settings, &m)
	delete(m, "isPrimary")
	bytes, err := json.Marshal(m)
	if err != nil {
		return settings
	}
	return bytes
}

func cloneTitle(content content.Content) string {
	title := content.Title + " Copy"
	if len(title) > 255 {
		title = title[:255]
	}
	return title
}

func cloneRoute(c content.Content, suffix string) string {
	route := ""
	switch c.Type {
	case "news":
		route = "/news/"
	case "event":
		route = "/event/"
	default:
		route = "/"
	}
	return route + suffix

}
