package htmlxx

import (
	"github.com/PuerkitoBio/goquery"
	"golang.org/x/net/html"
	"regexp"
	"strings"
)

func ExtractTextFromHTMLForIndexingOrEmpty(html string) string {
	text, err := ExtractTextFromHTMLForIndexing(html)
	if err != nil {
		return ""
	}
	return text
}

func ExtractTextFromHTMLForIndexing(html string) (string, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return "", err
	}

	doc.Find("head, script, style, [data-noindex]").Remove()

	doc.Find("img").Each(func(index int, element *goquery.Selection) {
		altText, exists := element.Attr("alt")
		if exists {
			element.ReplaceWithHtml(altText)
		} else {
			element.Remove()
		}
	})
	doc.Find("iframe").Each(func(_ int, element *goquery.Selection) {
		src, exists := element.Attr("src")
		if exists {
			element.ReplaceWithHtml(src)
		} else {
			element.Remove()
		}
	})

	text := ExtractTextFromNode(doc.Get(0))

	return BeautifyText(text), nil
}

func ExtractTextFromNode(n *html.Node) string {
	if n.Type == html.TextNode {
		return n.Data
	}

	var sb strings.Builder

	// For block elements, add a space before and after
	if isBlockElement(n.Data) {
		sb.WriteString("\n")
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		sb.WriteString(ExtractTextFromNode(c))
	}

	if isBlockElement(n.Data) {
		sb.WriteString("\n")
	}

	return sb.String()
}

func isBlockElement(tagName string) bool {
	blockElements := map[string]bool{
		"p":          true,
		"h1":         true,
		"h2":         true,
		"h3":         true,
		"h4":         true,
		"h5":         true,
		"h6":         true,
		"div":        true,
		"section":    true,
		"article":    true,
		"nav":        true,
		"header":     true,
		"footer":     true,
		"aside":      true,
		"address":    true,
		"blockquote": true,
		"pre":        true,
		"ul":         true,
		"ol":         true,
		"li":         true,
		"table":      true,
		"tr":         true,
		"td":         true,
		"th":         true,
		"form":       true,
		"fieldset":   true,
		"legend":     true,
		"dl":         true,
		"dt":         true,
		"dd":         true,
		"hr":         true,
		"br":         true,
	}

	return blockElements[strings.ToLower(tagName)]
}

var reExtraSpaces = regexp.MustCompile(`\s{2,}`)

func BeautifyText(s string) string {
	lines := strings.Split(s, "\n")
	var processedLines []string

	for _, line := range lines {
		line = reExtraSpaces.ReplaceAllString(line, " ")
		line = strings.TrimSpace(line)

		// Skip the line if it's empty or contains only spaces/tabs
		if len(line) == 0 {
			continue
		}

		processedLines = append(processedLines, line)
	}

	return strings.Join(processedLines, "\n")
}
