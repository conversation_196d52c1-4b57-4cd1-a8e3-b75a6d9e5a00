package handlers

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/errx"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/auth/permissions"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/content/admin"
	"contentmanager/pkgs/content/resources"
	"contentmanager/pkgs/structure"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"github.com/tidwall/sjson"
	"gorm.io/gorm"
	"strings"
)

type (
	StructureDTO struct {
		auth.TenantWideBase
		Name          string
		Template      string
		FormStructure json.RawMessage
	}
	SearchQuery struct {
		pagx.Query
		sortx.SortingQuery
		Search string
		Active *bool
	}
)

func (StructureDTO) GetScopeEntity() string {
	return "cm.structure"
}

func (s StructureDTO) Validate() error {
	errMap := map[string]string{}

	if len(s.Name) == 0 {
		errMap["Name"] = "Name is required. "
	}
	if s.FormStructure != nil && len(s.Template) == 0 {
		errMap["Template"] = "Invalid HTML - If a form structure is provided, HTML is required"
	}
	if _, err := handlebars.Parse(s.Template); err != nil {
		errMap["Template"] = "Invalid HTML - " + err.Error()
	}

	if len(errMap) > 0 {
		return errx.NewValidationError(errMap)
	}
	return nil
}

func Search(r *shared.AppContext, q SearchQuery) result.Result[pagx.Paginated[structure.Structure]] {
	var pag pagx.Paginated[structure.Structure]

	tx := r.TenantDatabase()
	if len(q.Search) > 0 {
		tx = tx.Where(" structure.name ilike ( ? ) ", `%`+q.Search+`%`)
	}

	if q.Active != nil {
		tx = tx.Where("active = ?", q.Active)
	} else {
		tx = tx.Where("active = ?", true)
	}

	tx = tx.Order(q.GetSortingSQL())

	return result.Check(pag, pagination.Paginate(tx, q.Query, &pag))
}

func GetStructureById(r *shared.AppContext, id uuid.UUID) result.Result[structure.Structure] {
	//var pag pagx.Paginated[Structure]
	var s structure.Structure
	if err := r.TenantDatabase().First(&s, "id = ?", id).Error; err != nil {
		return result.Error(err, structure.Structure{})
	}

	return result.Success(s)
}

func Create(r *shared.AppContext, s StructureDTO) result.Result[uuid.UUID] {
	if err := s.Validate(); err != nil {
		return result.Error(err, uuid.Nil)
	}

	structure := structure.Structure{
		Active:        true,
		Name:          s.Name,
		Template:      s.Template,
		FormStructure: s.FormStructure,
	}
	structure.Track(r.AppTime().NowUTC(), r.Account().ID)

	res := result.Check(structure.ID, r.TenantDatabase().Create(&structure).Error)
	if res.IsSuccess() {
		onStructureChanged(r, structure)
	}
	return res
}

func Update(r *shared.AppContext, id uuid.UUID, s StructureDTO) result.EmptyResult {
	var structure structure.Structure
	if err := r.TenantDatabase().First(&structure, "id = ?", id).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	structure.Name = s.Name
	structure.Template = s.Template
	structure.FormStructure = s.FormStructure

	if err := s.Validate(); err != nil {
		return result.ErrorEmpty(err)
	}

	structure.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&structure).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	onStructureChanged(r, structure)

	return result.SuccessEmpty()
}

func Delete(r *shared.AppContext, id uuid.UUID) result.EmptyResult {
	var s structure.Structure
	if err := r.TenantDatabase().First(&s, "id = ?", id).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	s.Active = false
	s.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&s).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	onStructureChanged(r, s)

	return result.SuccessEmpty()
}

type CloneDTO struct {
	ID   uuid.UUID
	Name string
}

// CloneStructureAndFragments
// The current business need is for fragments to have this behaviour, however, we could easily make it so that this is not scoped to `fragment`
func CloneStructureAndFragments(r *shared.AppContext, dto CloneDTO) result.EmptyResult {
	now := r.AppTime().NowUTC()
	accountID := r.Account().ID

	var s structure.Structure
	if err := r.TenantDatabase().First(&s, "id = ?", dto.ID).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	clonedStructure := structure.Structure{
		Active:        true,
		Name:          dto.Name,
		Template:      s.Template,
		FormStructure: s.FormStructure,
	}
	clonedStructure.Track(now, accountID)

	var fragments []content.Content
	if err := r.TenantDatabase().
		Where("structure_id = ?", dto.ID).
		Where("active").
		Where("type = ?", admin.Fragment).
		Find(&fragments).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	// Create a map of resource_refs to avoid needing to scan the cloned fragments.
	fragmentIds := slicexx.Select(fragments, func(f content.Content) uuid.UUID {
		return f.ID
	})
	refs := []resources.ResourceRef{}
	if err := r.TenantDatabase().
		Where(pgxx.FieldInArray("entity_id", fragmentIds)).
		Find(&refs).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	var refMap = map[uuid.UUID][]resources.ResourceRef{}
	for _, ref := range refs {
		refMap[ref.EntityID] = append(refMap[ref.EntityID], ref)
	}
	clonedRefs := []resources.ResourceRef{}

	if err := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&clonedStructure).Error; err != nil {
			return err
		}
		for i := 0; i < len(fragments); i++ {
			clonedContentID := uuid.NewV4()
			// Use the existing fragment.ID to find the resource_refs that need to be cloned.
			if refs, ok := refMap[fragments[i].ID]; ok {
				for _, resourceRef := range refs {
					resourceRef.EntityID = clonedContentID
					clonedRefs = append(clonedRefs, resources.ResourceRef{
						EntityID:   clonedContentID,
						EntityType: resourceRef.EntityType,
						Resource:   resourceRef.Resource,
					})
				}
			}
			fragments[i].ID = clonedContentID
			fragments[i].StructureID = &clonedStructure.ID
			if settings, err := sjson.Set(string(fragments[i].Settings), "ClonedFrom", clonedContentID.String()); err == nil {
				fragments[i].Settings = []byte(settings)
			} else {
				r.Logger().Warn().Err(err).Msg("[CloneStructureAndFragments] Failed to set id in settings")
			}
			if strings.Contains(fragments[i].Title, s.Name) {
				fragments[i].Title = strings.Replace(fragments[i].Title, s.Name, dto.Name, 1)
			} else {
				fragments[i].Title = fragments[i].Title + " - " + dto.Name
			}
			fragments[i].Path = utils.SanitizeLTree(clonedContentID.String())
			fragments[i].Owner = accountID
			fragments[i].Publisher = accountID
			fragments[i].Created = now
			fragments[i].Updated = now
		}
		if err := tx.Create(&fragments).Error; err != nil {
			return err
		}
		return tx.Create(&clonedRefs).Error
	}); err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func (s StructureDTO) GetID() uuid.UUID {
	return uuid.Nil
}
func (s StructureDTO) GetType() string {
	return permissions.Template
}
func (s StructureDTO) GetSites() []uuid.UUID {
	return nil
}
func (s StructureDTO) GetDepartmentID() *uuid.UUID {
	return nil
}

var _ commonModels.IContent = (*StructureDTO)(nil)
