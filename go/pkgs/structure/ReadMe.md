## Structures

List of all available components:
- `checkbox`: `boolean` type
- `select`: `string` type (options are provided in the `options` field: `options: [{text: string, value: string}]`)
- `document`: `string` type (a relative path to the document/folder: `/folder/uuid` or `/document/uuid`)
- `image`: `{src: string, alt: string}` type (`src` is a relative path to the image: `/images/uuid`)
- `textarea`: `string` type (multiple lines)
- `text`: `string` type (single line)
- `rich-text`: `string` type (HTML) (see `LexicalEditorOptions` in [RichTextEditor.tsx](../../../react/src/pkgs/lexical-editor/RichTextEditor.tsx))
- `contact-form-link`: `string` type (a relative link to a form). Options: `types: ['email', 'fragment']`, `allowedStructures: [string]`. See [FormLink.tsx](../../../react/src/pkgs/forms/FormLink.tsx)
- `email`: `string` type (a valid email address)
- `date`: `string` type (a date in `YYYY-MM-DD` format)
- `query`: `{queryID: string, templateID?: string, pageSize?: number}` type 

The related `tsx` form renderer: [DctComponentList.tsx](../../../react/src/pkgs/form-renderer/DctComponentList.tsx)

### uniq_scope
- uniq_scope: `string` type (global | site)
