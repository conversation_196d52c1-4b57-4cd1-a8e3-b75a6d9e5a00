package public

import "contentmanager/library/httpService"

func AddNotifications(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/sys/notifications", func(router httpService.Router) {
		router.Get("/preview", PreviewGet)
		router.Get("/preview-district", PreviewDistrictGet)

		router.Get("/subscribe", SubscribeGet)
		router.Get("/api/subscribe", SubscribeAPI_GET)
		router.Post("/subscribe", SubscribePost)
		router.Post("/api/subscribe", SubscribeAPI_POST)

		router.Get("/manage/:Secret/:TopicId/unsubscribe", UnsubscribeGet)

		router.Get("/manage/:secret", ManageGet)
		router.Get("/api/manage/:secret", ManageAPI_GET)
		router.Post("/manage/:secret", ManagePost)
		router.Post("/api/manage/:secret", ManageAPI_POST)

		// it might be worth to add a separate confirmation page here with <a href="../">Manage</a>
		router.Post("/manage/:secret/confirm", ConfirmGet)
		router.Get("/manage/:secret/confirm", ConfirmGet)

		router.Get("/get-link", GetManageLinkGet)
		router.Post("/get-link", GetManageLinkPost)
	})
	return r
}
