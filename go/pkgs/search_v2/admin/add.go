package admin

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/search_v2"
	"contentmanager/pkgs/search_v2/index"
	"contentmanager/pkgs/search_v2/spell"
	"errors"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"strconv"
	"time"
)

func AddAdminSearch(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v1/index", func(router httpService.Router) {

		router.Get("/search", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery search_v2.Query
		}) {
			start := time.Now()
			res := search_v2.GetSearchResults(r, params.FromQuery)
			duration := time.Since(start).Milliseconds()

			w.<PERSON><PERSON>().Set("X-Response-Time", strconv.FormatInt(duration, 10))

			utils.WriteResultJSON(w, res)
		})

		router.Get("/spellcheck", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery struct {
				SearchText string
			}
		}) {
			res, err := spell.Check(params.FromQuery.SearchText)
			if err != nil {
				r.Logger().Error().Err(err).Msg("Failed to check spelling")
				utils.WriteError(w, errors.New("Failed to check spelling. "))
				return
			}

			utils.WriteResponseJSON(w, res, nil)
		})

		router.Put("/content/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"required"`
			}
		}) {
			ctx, err := index.ContextFromAppContext(r)
			if err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			err = index.CreateIndexByContentID(*ctx, params.FromPath.ID)
			if err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			utils.WriteResponseJSON(w, nil, nil)
		})

		router.Get("/content/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"required"`
			}
		}) {
			var searchData = []search_v2.SearchData{}
			if err := r.TenantDatabase().Where("ext_id = ?", params.FromPath.ID).Find(&searchData).Error; err != nil {
				utils.WriteError(w, err)
				return
			}

			utils.WriteResponseJSON(w, searchData, nil)
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())
	return r
}
