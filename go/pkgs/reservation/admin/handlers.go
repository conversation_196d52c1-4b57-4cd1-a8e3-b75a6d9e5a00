package admin

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/auth/permissions"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/reservation"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

var (
	ErrUnsupportedTable = errors.New("the provided table doesn't support editing sessions")
)

func UpdateEditingSession(r *shared.AppContext, table string, entityID uuid.UUID, params reservation.ReservableParams) result.Result[reservation.ReservableEntity] {
	var res reservation.ReservableEntity
	switch table {
	case "content":
		var c content.Content
		err := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("id = ?", entityID).First(&c).Error; err != nil {
				return err
			}
			if err := permissions.Evaluate(r.Account(), c, "update"); err != nil {
				return err
			}
			if err := c.StartOrExtendSession(params, r.Account()); err != nil {
				return err
			}
			return tx.Save(&c).Error
		})
		if err != nil {
			return result.Error(err, res)
		}
		res.ID = c.ID
		res.Reservable = c.Reservable
		return result.Success(res)
	default:
		return result.Error(ErrUnsupportedTable, res)
	}
}

func EndEditingSession(r *shared.AppContext, table string, editingSession *time.Time, entityID uuid.UUID) result.EmptyResult {
	if editingSession == nil {
		return result.ErrorEmpty(errors.New("invalid reservation key"))
	}
	switch table {
	case "content":
		if err := r.TenantDatabase().
			Table("content").
			Where("id = ?", entityID).
			Where("editing_session = ?", editingSession).
			Where("current_editor = ?", r.Account().ID).
			Update("editing_session", nil).
			Error; err != nil {
			return result.ErrorEmpty(err)
		}
		return result.SuccessEmpty()
	default:
		return result.ErrorEmpty(ErrUnsupportedTable)
	}
}

func EndExtendedLock(r *shared.AppContext, table string, editingSession *time.Time, entityID uuid.UUID) result.Result[reservation.Reservable] {
	switch table {
	case "content":
		var c content.Content
		err := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("id = ?", entityID).First(&c).Error; err != nil {
				return err
			}
			if !c.IsEditingSessionForUserOrAvailable(editingSession, &r.Account().ID) {
				return reservation.ErrEditingSessionConflict
			}
			c.ExtendedLock = nil
			return tx.Save(&c).Error
		})
		if err != nil {
			return result.Error(err, c.Reservable)
		}
		return result.Success(c.Reservable)
	default:
		return result.Error(ErrUnsupportedTable, reservation.Reservable{})
	}
}

func OverrideExtendedLock(r *shared.AppContext, table string, entityID uuid.UUID) result.Result[reservation.Reservable] {
	if !r.Account().IsAdmin {
		return result.Error(errors.New("must be an admin to override an extended lock"), reservation.Reservable{})
	}
	switch table {
	case "content":
		var c content.Content
		if err := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("id = ?", entityID).First(&c).Error; err != nil {
				return err
			}
			if c.IsSessionLocked(nil) {
				return reservation.ErrEditingSessionConflict
			}
			c.ExtendedLock = nil
			c.CurrentEditor = nil
			return tx.Save(&c).Error
		}); err != nil {
			return result.Error(err, c.Reservable)
		}
		return result.Success(c.Reservable)
	default:
		return result.Error(ErrUnsupportedTable, reservation.Reservable{})
	}
}
